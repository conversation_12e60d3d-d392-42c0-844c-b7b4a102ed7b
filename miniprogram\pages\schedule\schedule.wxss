/* schedule.wxss */
/*
 * 课程表页面样式文件
 *
 * 设计理念：
 * 1. 列表式布局：适合展示大量课程信息
 * 2. Tab切换设计：清晰的视图切换体验
 * 3. 卡片式课程项：每个课程独立的卡片展示
 * 4. 状态化设计：不同预约状态有不同的视觉反馈
 * 5. 响应式适配：混合使用px和rpx单位
 * 6. 固定头部滚动：顶部固定，内容区域滚动
 *
 * 页面结构：
 * - 顶部固定：视图切换Tab + 搜索框 + 日期筛选器
 * - 主体滚动：课程列表（支持分页加载）
 * - 底部：tabBar导航
 */

/*
 * 页面根元素样式
 * 设置页面的基础高度和渐变背景
 */
page {
  height: 100%;
  /* 保留渐变背景 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background-attachment: fixed;
}

/*
 * 页面容器样式
 * 确保页面容器占满整个可用高度
 */
.page {
  height: 100%;
  background: transparent;
}

/**
 * .container: 页面根容器样式
 *
 * 设计说明：
 * - 使用较小的内边距(12px)，为列表内容留出更多空间
 * - 底部预留tabBar和安全区域空间
 * - 统一的字体族设置
 *
 * 与profile页面的差异：
 * - profile页面：32rpx内边距，适合卡片式布局
 * - schedule页面：12px内边距，适合列表式布局
 */
.container {
  /*
   * 固定布局设置
   * 禁止根容器滚动，让顶部区域固定，只有内容区域滚动
   */
  height: 100%; /* 占满父容器高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁止根容器滚动 */

  /* 内边距：12px固定内边距，为列表内容提供紧凑布局 */
  padding: 12px;

  /*
   * 底部内边距：为tabBar和安全区域预留空间
   * calc(12px + 120rpx + env(safe-area-inset-bottom))
   * - 12px: 与顶部内边距保持一致
   * - 120rpx: tabBar高度
   * - env(safe-area-inset-bottom): 底部安全区域
   */
  padding-bottom: calc(12px + 120rpx + env(safe-area-inset-bottom));

  /* 透明背景，让page的渐变背景显示 */
  background: transparent;

  /* 统一字体族：与整个应用保持一致 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /* 盒模型：padding包含在总宽度内 */
  box-sizing: border-box;
}

/**
 * 视图切换栏样式
 *
 * 功能说明：
 * 包含Tab切换按钮和搜索框的容器
 * 为页面的主要交互区域提供布局基础
 */

/**
 * .view-section: 视图切换区域容器
 *
 * 布局特点：
 * - 100%宽度：占满父容器
 * - 无底部内边距：与下方内容紧密连接
 * - 统一字体：保持文字样式一致性
 */
.view-section {
  width: 100%;                      /* 宽度：占满父容器 */
  padding-bottom: 0px;              /* 底部内边距：0，与下方内容无间隙 */
  /* 字体族：继承统一的字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;                   /* 防止视图切换区域被压缩 */
}

/**
 * .filter-section: 筛选区域容器
 *
 * 预留样式：
 * 为未来可能的筛选功能预留的样式定义
 * 目前与view-section样式相同
 */
.filter-section {
  width: 100%;                      /* 宽度：占满父容器 */
  padding-bottom: 0px;              /* 底部内边距：0 */
  /* 字体族：统一字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * Tab切换相关样式
 *
 * 设计原理：
 * 1. 使用Flexbox实现等宽分布
 * 2. 圆角背景增强视觉层次
 * 3. 内边距提供点击区域
 * 4. 状态切换通过CSS类名控制
 */

/**
 * 顶部选项卡区域样式 - 与course-management页面保持一致
 *
 * 功能：包含主选项卡组件的容器
 * 布局：优雅的线条式设计，增强视觉层次感
 * 设计理念：从简单的booking-tabs升级为专业的TDesign选项卡
 */
.top-tabs-section {
  /*
   * 布局和定位
   */
  position: relative;

  /*
   * 背景设计 - 渐变背景增加层次感
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 精致的边框效果
   */
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  /*
   * 阴影效果 - 轻微的阴影增加浮起感
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   */
  padding: 0px 16px 0 16px;

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 底部装饰线 - 与内容区域的分隔
   */
  border-bottom: 1px solid #e7e7e7;

  /*
   * 层级控制
   */
  z-index: 10;


}



/**
 * 自定义线条选项卡样式 - 与course-management页面完全一致
 *
 * 功能：覆盖TDesign组件的默认样式
 * 目的：实现优雅的线条选项卡设计
 */
.custom-top-tabs {
  /*
   * 背景 - 完全透明，让容器背景显示
   */
  background-color: transparent;
  border: none;

  /*
   * 移除圆角 - 线条选项卡不需要圆角
   */
  border-radius: 0;

  /*
   * 溢出控制
   */
  overflow: visible;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 16px;

  /*
   * 布局控制
   */
  margin: 0;
  width: 100%;
  height: auto;

  /*
   * 最小高度设置为最小值以确保紧凑布局
   */
  min-height: 1px;
}

/**
 * TDesign线条选项卡导航区域样式
 */
.custom-top-tabs .t-tabs__nav {
  /*
   * 内边距 - 优化的内边距
   */
  padding: 0;
  height: auto;
  min-height: 1px;

  /*
   * 移除底部边框，使用父容器的边框
   */
  border-bottom: none;

  /*
   * 布局优化
   */
  display: flex;
  align-items: center;

  /*
   * 背景渐变效果
   */
  background: transparent;
}

/**
 * 线条选项卡项目样式
 */
.custom-top-tabs .t-tabs__item {
  /*
   * 字体设置 - 优化可读性
   */
  font-size: 16px !important;
  font-weight: 500;

  /*
   * 内边距 - 增加舒适的点击区域
   */
  padding: 14px 20px !important;

  /*
   * 高度控制
   */
  height: auto;
  line-height: 1.4;
  min-height: 44px;

  /*
   * 布局控制
   */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /*
   * 移除圆角和背景
   */
  border-radius: 0;
  background: transparent !important;

  /*
   * 底部边框 - 激活状态指示器
   */
  border-bottom: 3px solid transparent;

  /*
   * 过渡动画 - 更流畅的动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 文字颜色
   */
  color: #666666 !important;

  /*
   * 相对定位用于伪元素
   */
  position: relative;
}

/*
 * 选项卡项目的装饰效果
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 激活状态的线条选项卡
 */
.custom-top-tabs .t-tabs__item--active {
  /*
   * 文字颜色和字重
   */
  color: #0052d9 !important;
  font-weight: 600 !important;

  /*
   * 底部蓝色指示线 - 更粗更明显
   */
  border-bottom-color: #0052d9 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 文字阴影效果 - 增加层次感
   */
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);
}

/*
 * 选项卡项目的装饰效果
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * 激活状态的顶部装饰线
 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%; /* 激活时显示顶部装饰线 */
}

/**
 * 非激活状态的选项卡悬停效果
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  /*
   * 悬停时的文字颜色
   */
  color: #333333 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 悬停时的底部边框效果
   */
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;

  /*
   * 轻微的文字阴影
   */
  text-shadow: 0 0 1px rgba(51, 51, 51, 0.1);
}

/*
 * 悬停时的顶部装饰线
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%; /* 悬停时显示较短的顶部装饰线 */
}

/**
 * 底部指示线容器
 */
.custom-top-tabs .t-tabs__track {
  display: none; /* 隐藏默认的滑动指示器，使用border-bottom代替 */
}

/**
 * 搜索区域样式 - 与上方t-tabs协调设计
 *
 * 功能：包含搜索框的区域容器
 * 设计：与t-tabs的样式风格保持一致，形成统一的视觉语言
 */
.search-section {
  margin-top: 12px; /* 与上方t-tabs保持间距 */
  margin-bottom: 12px; /* 与下方内容保持间距 */
}

/**
 * 搜索和操作区域样式 - 与t-tabs风格协调
 */
.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 44px; /* 与t-tabs的高度保持一致 */

  /*
   * 背景设计 - 与t-tabs的渐变背景保持一致
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 与t-tabs的精致边框保持一致
   */
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  /*
   * 阴影效果 - 与t-tabs的轻微阴影保持一致
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   */
  padding: 6px 16px;

  /*
   * 过渡动画 - 与t-tabs保持一致
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}



/**
 * 展开状态布局 - 与course-management页面完全一致
 */
.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1; /* 占据search-actions-section的全部空间 */
}

/**
 * 展开的搜索输入框 - 与t-tabs风格协调
 */
.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 6px; /* 与外层容器的圆角保持协调 */
  padding: 8px 12px; /* 增加内边距，更舒适 */
  border: 1px solid #e7e7e7; /* 与t-tabs的边框颜色保持一致 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 与t-tabs的动画保持一致 */
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  height: 36px; /* 与外层容器高度协调 */

  /*
   * 轻微的内阴影，增加层次感
   */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow:
    0 0 0 2px rgba(0, 82, 217, 0.1), /* 聚焦阴影 */
    inset 0 1px 2px rgba(0, 0, 0, 0.02); /* 保持内阴影 */
}

.search-icon {
  color: #0052d9; /* 使用主题蓝色，与t-tabs的激活颜色保持一致 */
  margin-right: 8px; /* 适当增加间距 */
  flex-shrink: 0;
  font-size: 16px; /* 稍微增大图标，更醒目 */
  opacity: 0.8; /* 轻微透明，更柔和 */
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 15px; /* 稍微增大字体，提升可读性 */
  color: #333333;
  background: transparent;
  min-width: 0;
  height: 20px; /* 固定输入框高度 */
  line-height: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 400; /* 与t-tabs的字重保持协调 */
}

.search-input::placeholder {
  color: #999999;
  font-size: 15px;
  font-weight: 400;
}

.clear-icon {
  color: #999999;
  margin-left: 8px; /* 与搜索图标的间距保持一致 */
  flex-shrink: 0;
  cursor: pointer;
  font-size: 16px; /* 与搜索图标大小保持一致 */
  padding: 4px; /* 增加点击区域 */
  border-radius: 4px; /* 与整体圆角风格保持一致 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 与t-tabs的动画保持一致 */
  opacity: 0.8; /* 与搜索图标的透明度保持一致 */
}

.clear-icon:active {
  color: #0052d9; /* 点击时使用主题色 */
  background: rgba(0, 82, 217, 0.1); /* 轻微的主题色背景 */
  opacity: 1; /* 点击时完全不透明 */
  transform: scale(0.95); /* 轻微缩放效果 */
}

/**
 * 搜索展开动画 - 与course-management页面完全一致
 */
@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}



/* 日期选择器样式 */
.date-section {
  margin-bottom: 12px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0; /* 防止日期选择器被压缩 */
}

.date-tabs-scroll {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  overflow-x: auto;
  padding: 4px 0 6px 0;
  flex-shrink: 0; /* 防止日期选择器被压缩 */
  background: #fff;
  border-radius: 8px;
  margin: 4px 0;
  flex-shrink: 0; /* 防止日期选择器被压缩 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
}

.date-tabs-scroll::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/*
 * 通用滚动条美化样式
 * 适用于所有scroll-view组件
 */

/* 所有scroll-view的垂直滚动条 */
scroll-view::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

scroll-view::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05); /* 轨道背景 */
  border-radius: 3px;
  margin: 8px 0; /* 上下间距 */
}

scroll-view::-webkit-scrollbar-thumb {
  background: rgba(0, 82, 217, 0.3); /* 滑块颜色 */
  border-radius: 3px;
  /* 移除transition，避免影响滚动响应性 */
}

scroll-view::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 82, 217, 0.6); /* 悬停颜色 */
}

scroll-view::-webkit-scrollbar-thumb:active {
  background: rgba(0, 82, 217, 0.8); /* 激活颜色 */
}

/* 所有scroll-view的水平滚动条 */
scroll-view::-webkit-scrollbar:horizontal {
  height: 4px; /* 水平滚动条高度 */
}

scroll-view::-webkit-scrollbar-track:horizontal {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  margin: 0 8px; /* 左右间距 */
}

scroll-view::-webkit-scrollbar-thumb:horizontal {
  background: rgba(0, 82, 217, 0.3);
  border-radius: 2px;
  /* 移除transition，避免影响滚动响应性 */
}

scroll-view::-webkit-scrollbar-thumb:horizontal:hover {
  background: rgba(0, 82, 217, 0.6);
}

scroll-view::-webkit-scrollbar-thumb:horizontal:active {
  background: rgba(0, 82, 217, 0.8);
}

/* 滚动条角落 */
scroll-view::-webkit-scrollbar-corner {
  background: transparent;
}
.date-tab {
  display: inline-block;
  width: 50px;
  text-align: center;
  margin-right: 8px;
  padding: 6px 0 0 0;
  cursor: pointer;
  min-height: 38px;
}
.date-tab:last-child {
  margin-right: 0;
}
.date-tab .tab-label {
  display: block;
  height: 18px;
  line-height: 18px;
}
.date-tab .tab-date {
  display: block;
  font-size: 12px;
  margin-top: 2px;
  height: 16px;
  line-height: 16px;
  color: #bbb;
}


/* 课程列表样式 - 使用原生滚动，彻底解决滚动响应性问题 */
.course-list {
  /*
   * 可滚动内容区域样式
   * 占用剩余空间，允许内容滚动
   */
  flex: 1; /* 占用剩余空间 */
  overflow-y: auto; /* 内容溢出时自动显示滚动条 */
  overflow-x: hidden; /* 禁止水平滚动 */

  /* 关键：设置固定高度，让滚动生效 */
  height: 0; /* 配合flex: 1使用 */
  min-height: 0; /* 确保可以缩小 */

  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * 原生滚动优化 - 最佳滚动响应性
   */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */

  /*
   * 隐藏滚动条的通用方法
   * 适用于不同浏览器和小程序环境
   */
  scrollbar-width: none; /* Firefox隐藏滚动条 */
  -ms-overflow-style: none; /* IE/Edge隐藏滚动条 */

  /* 移除所有可能干扰滚动的属性 */
  /* touch-action: auto; - 使用默认值 */
  /* scroll-behavior: auto; - 使用默认值 */
  /* pointer-events: auto; - 使用默认值 */
  /* position: relative; - 使用默认值 */
  /* z-index: 1; - 使用默认值 */
}

/*
 * 隐藏滚动条样式 - 保持滚动功能但不占用宽度
 */

/* 隐藏滚动条但保持滚动功能 */
.course-list::-webkit-scrollbar {
  width: 0px; /* 设置宽度为0，完全隐藏滚动条 */
  background: transparent; /* 透明背景 */
}

/* 隐藏滚动条轨道 */
.course-list::-webkit-scrollbar-track {
  background: transparent;
}

/* 隐藏滚动条滑块 */
.course-list::-webkit-scrollbar-thumb {
  background: transparent;
}

/* 隐藏滚动条角落 */
.course-list::-webkit-scrollbar-corner {
  background: transparent;
}

/* 时间轴样式 - 参考course-management */
.timeline-date {
  margin: 16px 0 8px 0;
  font-size: 15px;
  color: #0052d9;
  font-weight: bold;
  text-align: left;
  position: relative;
  padding-left: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 8px;
  height: 8px;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

.course-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  /* 保留客户喜欢的阴影效果 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;

  /* 彻底优化触摸响应性 */
  pointer-events: auto; /* 确保可以接收点击事件 */
  touch-action: manipulation; /* 允许触摸操作但优化响应 */

  /* 移除任何可能干扰滚动的属性 */
  /* overflow: hidden; - 移除，可能影响触摸 */
  /* cursor: pointer; - 移除，可能影响触摸 */
  /* transition: - 移除，可能影响滚动 */
  /* transform: - 移除，可能影响滚动 */
}

/* 完全移除:active、:hover等状态，避免干扰滚动响应性 */

/* 新加载卡片的优雅提示动画 - 方案1：滑入 + 顶部指示条 */
.course-card.new-loaded {
  animation: slide-in-up 0.5s ease-out;
  position: relative;
}

.course-card.new-loaded::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #0052d9, #40a9ff, #0052d9);
  border-radius: 12px 12px 0 0;
  animation: new-content-indicator 2s ease-out;
}

/* 方案2：仅滑入动画（更简洁）*/
.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

/* 方案3：淡入 + 轻微缩放 */
.course-card.fade-in {
  animation: fade-in-scale 0.7s ease-out;
}

/* 从下方滑入的动画 */
@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 淡入缩放动画 */
@keyframes fade-in-scale {
  0% {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 顶部蓝色指示条动画 */
@keyframes new-content-indicator {
  0% {
    opacity: 1;
    background: linear-gradient(90deg, #0052d9, #40a9ff, #0052d9);
  }
  70% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.course-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}

.course-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}

.course-status.booked {
  background-color: #e6f3ff;
  color: #0052d9;
}

.course-status.in-progress {
  background-color: #fff7e6;
  color: #d46b08;
}

.course-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}

.course-status.ended {
  background-color: #f0f0f0;
  color: #888;
}



.course-date {
  font-size: 14px;
  color: #0052d9;
  margin-top: 2px;
  margin-bottom: 2px;
  font-weight: 500;
}

.course-info-list {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
  margin-left: auto;
}

 

/* 移除自定义弹窗样式，使用系统默认弹窗 */

 

/* 统一 t-button 字体大小，保证禁用按钮和可预约按钮一致 */
.t-button {
  font-size: 16px !important;
}

 



/* 加载指示器样式 - 参考course-management */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 15px;
  padding: 16px 0 8px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.loading-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 15px;
  padding: 12px 0 8px 0;
  letter-spacing: 1px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  position: relative;
  opacity: 0.8;
}

.end-indicator::before,
.end-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60px;
  height: 1px;
  background: linear-gradient(to right, transparent, #d0d0d0, transparent);
}

.end-indicator::before {
  left: 20px;
}

.end-indicator::after {
  right: 20px;
}

/* 加载动画 */
@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}





 
